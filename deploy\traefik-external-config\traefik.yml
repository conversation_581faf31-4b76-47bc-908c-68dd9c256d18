# Traefik 内网环境配置
# 适用于内网环境，无需SSL配置
# 支持环境变量替换：${NETWORK_NAME}、${DOMAIN}等

global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API和Dashboard配置
api:
  dashboard: true
  insecure: true
  debug: false

# 健康检查端点
ping:
  entryPoint: "traefik"

# 入口点配置
entryPoints:
  web:
    address: ":80"
  traefik:
    address: ":8080"

# 服务发现配置
providers:
  swarm:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: ${NETWORK_NAME}
    watch: true

# 日志配置
log:
  level: DEBUG
  format: json

accessLog:
  format: json

# 指标配置
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# 中间件配置
http:
  middlewares:
    # 默认头部中间件
    default-headers:
      headers:
        frameDeny: true
        browserXssFilter: true
        contentTypeNosniff: true

    # 安全头部中间件
    secure-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
        accessControlMaxAge: 100
        hostsProxyHeaders:
          - "X-Forwarded-Host"
        referrerPolicy: "same-origin"

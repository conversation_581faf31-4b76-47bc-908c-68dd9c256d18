# CCSP Docker Swarm 部署脚本

本目录包含用于部署和管理 CCSP (Cloud Cryptographic Service Platform) Docker Swarm 集群的自动化脚本。

## 脚本概览

### 核心部署脚本

- **`deploy-ccsp.sh`** - 主部署脚本，协调整个部署流程
- **`swarm-manager-init.sh`** - Docker Swarm 管理节点初始化脚本
- **`swarm-worker-init.sh`** - Docker Swarm 工作节点初始化脚本
- **`swarm-init.sh`** - Docker Swarm 集群初始化脚本（兼容性脚本，调用管理节点脚本）
- **`setup-infrastructure.sh`** - 基础设施配置脚本（网络、存储、密钥等）
- **`deploy-stack.sh`** - Stack 部署和管理脚本
- **`deploy-console-intranet.sh`** - Console内网环境部署脚本（无SSL配置）
- **`deploy-traefik.sh`** - Traefik内网环境部署脚本（无SSL配置）

### 运维管理脚本

- **`node-join.sh`** - 节点加入脚本
- **`health-check.sh`** - 服务健康检查和监控脚本
- **`service-update.sh`** - 服务更新和回滚脚本

## 快速开始

### 1. 完整部署

```bash
# 完整部署 CCSP 环境
./scripts/deploy-ccsp.sh

# 部署到特定环境
./scripts/deploy-ccsp.sh -e staging

# 跳过集群初始化（如果集群已存在）
./scripts/deploy-ccsp.sh --skip-init
```

### 2. 分步部署

#### 管理节点初始化
```bash
# 在管理节点上初始化 Swarm 集群
./scripts/swarm-manager-init.sh

# 指定管理节点IP
./scripts/swarm-manager-init.sh -i *************

# 自定义网络配置
./scripts/swarm-manager-init.sh -n my-network -s ********/24
```

#### 工作节点加入
```bash
# 在工作节点上加入集群（使用管理节点显示的命令）
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m *************

# 或直接使用完整的加入命令
./scripts/swarm-worker-init.sh 'docker swarm join --token SWMTKN-1-xxx *************:2377'

# 添加节点标签
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m ************* -l env=prod,type=worker

# 创建本地存储卷（如果服务可能调度到此节点）
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m ************* --create-volumes
```

#### 继续部署
```bash
# 2. 配置基础设施（仅在管理节点运行）
./scripts/setup-infrastructure.sh

# 3. 部署应用栈
./scripts/deploy-stack.sh deploy -s ccsp -f docker-stack.yml
```

## 详细使用说明

### 集群初始化

#### 推荐方式（使用新脚本）

```bash
# 1. 在管理节点上初始化集群
./scripts/swarm-manager-init.sh

# 指定管理节点IP
./scripts/swarm-manager-init.sh -i *************

# 2. 在工作节点上加入集群
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m *************

# 或使用完整的加入命令
./scripts/swarm-worker-init.sh 'docker swarm join --token SWMTKN-1-xxx *************:2377'
```

#### 兼容方式（使用旧脚本）

```bash
# 在第一个 Manager 节点上初始化集群
./scripts/swarm-init.sh

# 在其他节点上加入集群
./scripts/node-join.sh -m <manager-ip> -t <join-token> -r worker
```

#### 新脚本优势

- **更好的错误处理**：包含网络连通性检查、重试机制
- **更清晰的职责分离**：管理节点和工作节点分别处理
- **更详细的日志输出**：便于问题诊断和调试
- **更灵活的配置选项**：支持自定义网络、标签等
- **更完善的验证机制**：确保操作成功完成

### 应用部署

```bash
# 部署 Stack
./scripts/deploy-stack.sh deploy -s ccsp

# 查看 Stack 状态
./scripts/deploy-stack.sh status -s ccsp

# 查看服务日志
./scripts/deploy-stack.sh logs management

# 扩缩容服务
./scripts/deploy-stack.sh scale management=5
```

### 健康检查

```bash
# 执行一次健康检查
./scripts/health-check.sh check -s ccsp

# 持续监控服务状态
./scripts/health-check.sh monitor -s ccsp -i 30

# 生成健康报告
./scripts/health-check.sh report -s ccsp
```

### 服务更新

```bash
# 更新服务镜像
./scripts/service-update.sh update -n management -i ccsp-management:v2.0.0

# 回滚服务
./scripts/service-update.sh rollback -n management

# 查看更新历史
./scripts/service-update.sh history -n management
```

## 环境配置

### 环境变量

部署脚本支持以下环境变量：

```bash
# 基本配置
export ENVIRONMENT=production          # 部署环境
export STACK_NAME=ccsp                # Stack 名称
export MANAGER_IP=*************       # Manager 节点 IP

# 网络配置
export NETWORK_NAME=ccsp-network      # 应用网络名称
export NETWORK_SUBNET=********/24     # 网络子网

# 监控配置
export ENABLE_MONITORING=true         # 启用监控
export HEALTH_CHECK_TIMEOUT=300       # 健康检查超时
export CHECK_INTERVAL=30              # 检查间隔

# 更新配置
export UPDATE_PARALLELISM=1           # 更新并行度
export UPDATE_DELAY=10s               # 更新延迟
```

### 目录结构

```
scripts/
├── deploy-ccsp.sh              # 主部署脚本
├── swarm-manager-init.sh       # 管理节点初始化（推荐）
├── swarm-worker-init.sh        # 工作节点初始化（推荐）
├── swarm-init.sh              # 集群初始化（兼容性脚本）
├── node-join.sh               # 节点加入（旧版本）
├── setup-infrastructure.sh    # 基础设施配置
├── deploy-stack.sh            # Stack 部署
├── deploy-console-intranet.sh # Console内网部署（无SSL）
├── deploy-traefik.sh          # Traefik内网部署（无SSL）
├── health-check.sh            # 健康检查
├── service-update.sh          # 服务更新
└── README.md                  # 本文档
```

## 故障排除

### 常见问题

1. **集群初始化失败**
   ```bash
# 检查 Docker 服务状态
   systemctl status docker
   
   # 检查防火墙设置
   firewall-cmd --list-ports
```

2. **服务部署失败**
   ```bash
# 查看服务详细状态
   docker service ps <service-name> --no-trunc
   
   # 查看服务日志
   docker service logs <service-name>
```

3. **健康检查失败**
   ```bash
# 检查服务端点
   curl -v http://<service-ip>:<port>/health
   
   # 检查网络连通性
   docker network ls
   docker network inspect ccsp-network
```

### 日志位置

- 部署日志：脚本输出到控制台
- 健康检查日志：`/var/log/swarm-health.log`
- Docker 服务日志：`docker service logs <service-name>`

### 监控和告警

健康检查脚本支持持续监控和告警：

```bash
# 启动监控（后台运行）
nohup ./scripts/health-check.sh monitor -s ccsp -i 30 > /var/log/swarm-monitor.log 2>&1 &

# 设置告警阈值
./scripts/health-check.sh monitor -s ccsp -t 3
```

## 安全注意事项

1. **密钥管理**：所有敏感信息使用 Docker Secrets 存储
2. **网络隔离**：数据库网络设置为内部网络
3. **访问控制**：仅在 Manager 节点执行管理操作
4. **日志安全**：避免在日志中记录敏感信息
5. **内网环境**：本项目配置为内网使用，无SSL/HTTPS配置

## 性能优化

1. **资源限制**：在 docker-stack.yml 中设置合适的资源限制
2. **副本分布**：使用 placement constraints 确保副本分散部署
3. **健康检查**：调整健康检查间隔和超时时间
4. **更新策略**：根据业务需求调整滚动更新参数

## 备份和恢复

```bash
# 备份 Docker Secrets 和 Configs
docker secret ls --format "{{.Name}}" | xargs -I {} docker secret inspect {}
docker config ls --format "{{.Name}}" | xargs -I {} docker config inspect {}

# 备份存储卷
docker run --rm -v ccsp-console-data:/data -v $(pwd):/backup alpine tar czf /backup/ccsp-console-data-backup.tar.gz -C /data .
```

## 支持和维护

- 定期检查脚本更新
- 监控集群资源使用情况
- 定期备份重要数据
- 保持 Docker 版本更新

如有问题，请查看日志文件或联系运维团队。
main "$@"
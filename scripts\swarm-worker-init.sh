# CCSP Docker Swarm 部署脚本

本目录包含用于部署和管理 CCSP (Cloud Cryptographic Service Platform) Docker Swarm 集群的自动化脚本。

## 脚本概览

### 核心部署脚本

- **`deploy-ccsp.sh`** - 主部署脚本，协调整个部署流程
- **`swarm-manager-init.sh`** - Docker Swarm 管理节点初始化脚本
- **`swarm-worker-init.sh`** - Docker Swarm 工作节点初始化脚本
- **`swarm-init.sh`** - Docker Swarm 集群初始化脚本（兼容性脚本，调用管理节点脚本）
- **`setup-infrastructure.sh`** - 基础设施配置脚本（网络、存储、密钥等）
- **`deploy-stack.sh`** - Stack 部署和管理脚本
- **`deploy-console-intranet.sh`** - Console内网环境部署脚本（无SSL配置）
- **`deploy-traefik.sh`** - Traefik内网环境部署脚本（无SSL配置）

### 运维管理脚本

- **`node-join.sh`** - 节点加入脚本
- **`health-check.sh`** - 服务健康检查和监控脚本
- **`service-update.sh`** - 服务更新和回滚脚本

## 快速开始

### 1. 完整部署

```bash
# 完整部署 CCSP 环境
./scripts/deploy-ccsp.sh

# 部署到特定环境
./scripts/deploy-ccsp.sh -e staging

# 跳过集群初始化（如果集群已存在）
./scripts/deploy-ccsp.sh --skip-init
```

### 2. 分步部署

#### 管理节点初始化
```bash
# 在管理节点上初始化 Swarm 集群
./scripts/swarm-manager-init.sh

# 指定管理节点IP
./scripts/swarm-manager-init.sh -i *************

# 自定义网络配置
./scripts/swarm-manager-init.sh -n my-network -s ********/24
```

#### 工作节点加入
```bash
# 在工作节点上加入集群（使用管理节点显示的命令）
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m *************

# 或直接使用完整的加入命令
./scripts/swarm-worker-init.sh 'docker swarm join --token SWMTKN-1-xxx *************:2377'

# 添加节点标签
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m ************* -l env=prod,type=worker

# 创建本地存储卷（如果服务可能调度到此节点）
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m ************* --create-volumes
```

#### 继续部署
```bash
# 2. 配置基础设施（仅在管理节点运行）
./scripts/setup-infrastructure.sh

# 3. 部署应用栈
./scripts/deploy-stack.sh deploy -s ccsp -f docker-stack.yml
```

## 详细使用说明

### 集群初始化

#### 推荐方式（使用新脚本）

```bash
# 1. 在管理节点上初始化集群
./scripts/swarm-manager-init.sh

# 指定管理节点IP
./scripts/swarm-manager-init.sh -i *************

# 2. 在工作节点上加入集群
./scripts/swarm-worker-init.sh -t SWMTKN-1-xxx -m *************

# 或使用完整的加入命令
./scripts/swarm-worker-init.sh 'docker swarm join --token SWMTKN-1-xxx *************:2377'
```

#### 兼容方式（使用旧脚本）

```bash
# 在第一个 Manager 节点上初始化集群
./scripts/swarm-init.sh

# 在其他节点上加入集群
./scripts/node-join.sh -m <manager-ip> -t <join-token> -r worker
```

#### 新脚本优势

- **更好的错误处理**：包含网络连通性检查、重试机制
- **更清晰的职责分离**：管理节点和工作节点分别处理
- **更详细的日志输出**：便于问题诊断和调试
- **更灵活的配置选项**：支持自定义网络、标签等
- **更完善的验证机制**：确保操作成功完成
- **智能存储卷管理**：工作节点可选择性创建本地存储卷

#### 存储卷管理策略

**网络：**
- Overlay网络由管理节点创建，自动同步到工作节点
- 工作节点无需手动创建网络

**存储卷：**
- **管理节点**：创建所有存储卷
- **工作节点**：默认不创建存储卷，可使用`--create-volumes`选项创建

**存储卷分类：**
- **必需卷**（工作节点自动创建）：
  - `ccsp-console-logs`：应用日志（各节点独立）
  - `prometheus-data`：监控数据（如果调度到此节点）
  - `grafana-data`：仪表板数据（如果调度到此节点）
  - `traefik-logs`：代理日志（如果调度到此节点）

- **可选卷**（需要手动创建或使用节点约束）：
  - `ccsp-console-data`：应用数据（建议固定节点）
  - `ccsp-console-config`：配置数据（建议使用Docker Config）
  - `mysql-data`：数据库数据（建议固定在管理节点）
  - `redis-data`：缓存数据（建议固定在管理节点）

**最佳实践：**
- 对于有状态服务（数据库），使用节点约束固定到特定节点
- 对于无状态服务，可以在所有节点创建存储卷
- 使用Docker Config/Secret管理配置数据

### 应用部署

```bash
# 部署 Stack
./scripts/deploy-stack.sh deploy -s ccsp

# 查看 Stack 状态
./scripts/deploy-stack.sh status -s ccsp

# 查看服务日志
./scripts/deploy-stack.sh logs management

# 扩缩容服务
./scripts/deploy-stack.sh scale management=5
```

### 健康检查

```bash
# 执行一次健康检查
./scripts/health-check.sh check -s ccsp

# 持续监控服务状态
./scripts/health-check.sh monitor -s ccsp -i 30

# 生成健康报告
./scripts/health-check.sh report -s ccsp
```

### 服务更新

```bash
# 更新服务镜像
./scripts/service-update.sh update -n management -i ccsp-management:v2.0.0

# 回滚服务
./scripts/service-update.sh rollback -n management

# 查看更新历史
./scripts/service-update.sh history -n management
```

## 环境配置

### 环境变量

部署脚本支持以下环境变量：

```bash
# 基本配置
export ENVIRONMENT=production          # 部署环境
export STACK_NAME=ccsp                # Stack 名称
export MANAGER_IP=*************       # Manager 节点 IP

# 网络配置
export NETWORK_NAME=ccsp-network      # 应用网络名称
export NETWORK_SUBNET=********/24     # 网络子网

# 监控配置
export ENABLE_MONITORING=true         # 启用监控
export HEALTH_CHECK_TIMEOUT=300       # 健康检查超时
export CHECK_INTERVAL=30              # 检查间隔

# 更新配置
export UPDATE_PARALLELISM=1           # 更新并行度
export UPDATE_DELAY=10s               # 更新延迟
```

### 目录结构

```
scripts/
├── deploy-ccsp.sh              # 主部署脚本
├── swarm-manager-init.sh       # 管理节点初始化（推荐）
├── swarm-worker-init.sh        # 工作节点初始化（推荐）
├── swarm-init.sh              # 集群初始化（兼容性脚本）
├── node-join.sh               # 节点加入（旧版本）
├── setup-infrastructure.sh    # 基础设施配置
├── deploy-stack.sh            # Stack 部署
├── deploy-console-intranet.sh # Console内网部署（无SSL）
├── deploy-traefik.sh          # Traefik内网部署（无SSL）
├── health-check.sh            # 健康检查
├── service-update.sh          # 服务更新
└── README.md                  # 本文档
```

## 故障排除

### 常见问题

1. **集群初始化失败**
   ```bash
# 检查 Docker 服务状态
   systemctl status docker
   
   # 检查防火墙设置
   firewall-cmd --list-ports
```

2. **服务部署失败**
   ```bash
# 查看服务详细状态
   docker service ps <service-name> --no-trunc
   
   # 查看服务日志
   docker service logs <service-name>
```

3. **健康检查失败**
   ```bash
# 检查服务端点
   curl -v http://<service-ip>:<port>/health
   
   # 检查网络连通性
   docker network ls
   docker network inspect ccsp-network
```

### 日志位置

- 部署日志：脚本输出到控制台
- 健康检查日志：`/var/log/swarm-health.log`
- Docker 服务日志：`docker service logs <service-name>`

### 监控和告警

健康检查脚本支持持续监控和告警：

```bash
# 启动监控（后台运行）
nohup ./scripts/health-check.sh monitor -s ccsp -i 30 > /var/log/swarm-monitor.log 2>&1 &

# 设置告警阈值
./scripts/health-check.sh monitor -s ccsp -t 3
```

## 安全注意事项

1. **密钥管理**：所有敏感信息使用 Docker Secrets 存储
2. **网络隔离**：数据库网络设置为内部网络
3. **访问控制**：仅在 Manager 节点执行管理操作
4. **日志安全**：避免在日志中记录敏感信息
5. **内网环境**：本项目配置为内网使用，无SSL/HTTPS配置

## 性能优化

1. **资源限制**：在 docker-stack.yml 中设置合适的资源限制
2. **副本分布**：使用 placement constraints 确保副本分散部署
3. **健康检查**：调整健康检查间隔和超时时间
4. **更新策略**：根据业务需求调整滚动更新参数

## 备份和恢复

```bash
# 备份 Docker Secrets 和 Configs
docker secret ls --format "{{.Name}}" | xargs -I {} docker secret inspect {}
docker config ls --format "{{.Name}}" | xargs -I {} docker config inspect {}

# 备份存储卷
docker run --rm -v ccsp-console-data:/data -v $(pwd):/backup alpine tar czf /backup/ccsp-console-data-backup.tar.gz -C /data .
```

## 支持和维护

- 定期检查脚本更新
- 监控集群资源使用情况
- 定期备份重要数据
- 保持 Docker 版本更新

如有问题，请查看日志文件或联系运维团队。
    done

    log_info "可选存储卷信息（未自动创建）："
    for volume_info in "${optional_volumes[@]}"; do
        volume_name=$(echo $volume_info | cut -d':' -f1)
        volume_desc=$(echo $volume_info | cut -d':' -f2)
        echo "  - ${volume_name}: ${volume_desc}"
    done

    echo ""
    log_info "如需创建可选存储卷，请手动运行："
    for volume_info in "${optional_volumes[@]}"; do
        volume_name=$(echo $volume_info | cut -d':' -f1)
        echo "  docker volume create ${volume_name}"
    done
}

# 加入Swarm集群
join_swarm() {
    log_step "加入Docker Swarm集群..."
    
    local attempt=1
    while [[ $attempt -le $RETRY_COUNT ]]; do
        log_info "尝试加入集群 (第 ${attempt}/${RETRY_COUNT} 次)..."
        
        if docker swarm join --token "$JOIN_TOKEN" "${MANAGER_IP}:${MANAGER_PORT}"; then
            log_info "成功加入Swarm集群"
            return 0
        else
            log_warn "加入集群失败 (第 ${attempt}/${RETRY_COUNT} 次)"
            
            if [[ $attempt -lt $RETRY_COUNT ]]; then
                log_info "等待 ${RETRY_DELAY} 秒后重试..."
                sleep "$RETRY_DELAY"
            fi
            
            ((attempt++))
        fi
    done
    
    log_error "加入集群失败，已重试 ${RETRY_COUNT} 次"
    log_error "请检查："
    log_error "  1. 加入令牌是否正确"
    log_error "  2. 管理节点地址是否正确"
    log_error "  3. 网络连通性"
    log_error "  4. 防火墙配置"
    exit 1
}

# 配置工作节点标签
configure_worker_labels() {
    log_step "配置工作节点标签..."
    
    # 获取当前节点ID
    local node_id=$(docker info | grep "NodeID" | awk '{print $2}')
    
    if [[ -z "$node_id" ]]; then
        log_warn "无法获取节点ID，跳过标签配置"
        return
    fi
    
    # 等待节点信息同步
    sleep 2
    
    # 添加基本标签（这些标签需要在管理节点上设置）
    log_info "节点ID: $node_id"
    log_info "基本标签将由管理节点自动设置"
    
    # 如果指定了自定义标签，提示用户在管理节点上设置
    if [[ -n "$NODE_LABELS" ]]; then
        log_info "要设置自定义标签，请在管理节点上运行："
        IFS=',' read -ra LABELS <<< "$NODE_LABELS"
        for label in "${LABELS[@]}"; do
            echo "  docker node update --label-add $label $node_id"
        done
    fi
}

# 验证加入状态
verify_join_status() {
    log_step "验证加入状态..."
    
    # 等待状态同步
    sleep 3
    
    if docker info | grep -q "Swarm: active"; then
        local node_id=$(docker info | grep "NodeID" | awk '{print $2}')
        local manager_addr=$(docker info | grep "Manager Addresses" | cut -d':' -f2- | xargs)
        
        log_info "节点成功加入Swarm集群"
        log_info "节点ID: $node_id"
        log_info "管理节点地址: $manager_addr"
        
        # 显示节点信息
        echo ""
        log_info "节点信息:"
        docker node ls 2>/dev/null || log_warn "无法显示节点列表（需要管理节点权限）"
        
        return 0
    else
        log_error "节点加入验证失败"
        return 1
    fi
}

# 显示后续操作提示
show_next_steps() {
    echo ""
    echo "=================================="
    echo "工作节点加入完成"
    echo "=================================="
    echo ""
    log_info "后续操作："
    echo "  1. 在管理节点上查看集群状态: docker node ls"
    echo "  2. 为节点添加标签: docker node update --label-add <key>=<value> <node-id>"
    echo "  3. 部署服务到此节点: 使用节点标签进行调度"
    if [[ "$CREATE_VOLUMES" == "true" ]]; then
        echo "  4. 存储卷已创建，服务可以调度到此节点"
    else
        echo "  4. 如需运行需要存储卷的服务，请使用 --create-volumes 重新运行"
    fi
    echo ""
    log_info "常用命令："
    echo "  - 查看节点信息: docker info"
    echo "  - 查看存储卷: docker volume ls"
    echo "  - 离开集群: docker swarm leave"
    echo "  - 查看服务: docker service ls"
    echo ""
    log_info "存储卷说明："
    echo "  - 必需卷: 已自动创建（如果使用了 --create-volumes）"
    echo "  - 可选卷: 根据服务调度需求手动创建"
    echo "  - 数据一致性: 使用节点约束确保数据服务固定节点"
    echo ""
    log_info "注意："
    echo "  - 此节点已配置为内网环境，无SSL配置"
    echo "  - 确保防火墙开放必要端口"
    echo "  - 节点管理操作需要在管理节点上执行"
}

# 主函数
main() {
    echo "Docker Swarm 工作节点初始化脚本"
    echo "适用于内网环境，无SSL配置"
    echo ""
    
    # 解析命令行参数
    parse_args "$@"
    
    # 验证参数
    validate_args
    
    log_info "开始初始化Docker Swarm工作节点..."
    log_info "管理节点: ${MANAGER_IP}:${MANAGER_PORT}"
    log_info "加入令牌: ${JOIN_TOKEN:0:20}..."
    if [[ -n "$NODE_LABELS" ]]; then
        log_info "节点标签: ${NODE_LABELS}"
    fi
    echo ""
    
    # 执行初始化步骤
    check_docker
    check_connectivity
    check_swarm_status
    join_swarm
    create_local_volumes
    configure_worker_labels
    verify_join_status
    show_next_steps
    
    echo ""
    log_info "Docker Swarm 工作节点初始化完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

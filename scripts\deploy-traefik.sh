#!/bin/bash

# Traefik 内网环境部署脚本
# 适用于内网环境，无需SSL配置

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
STACK_NAME=${STACK_NAME:-"traefik"}
NETWORK_NAME=${NETWORK_NAME:-"ccsp-network"}
DOMAIN=${DOMAIN:-"ccsp.local"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "Traefik 内网环境部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -s, --stack <NAME>      指定Stack名称 (默认: traefik)"
    echo "  -n, --network <NAME>    指定网络名称 (默认: ccsp-network)"
    echo "  -d, --domain <DOMAIN>   指定域名 (默认: ccsp.local)"
    echo "  -h, --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认配置部署"
    echo "  $0 -s my-traefik        # 指定Stack名称"
    echo "  $0 -d example.local     # 指定域名"
    echo ""
    echo "注意:"
    echo "  - 此脚本适用于内网环境，无SSL配置"
    echo "  - 需要先初始化Docker Swarm集群"
    echo "  - 需要先创建ccsp-network网络"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--stack)
                STACK_NAME="$2"
                shift 2
                ;;
            -n|--network)
                NETWORK_NAME="$2"
                shift 2
                ;;
            -d|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 检查Docker Swarm状态
check_swarm_status() {
    if ! docker info | grep -q "Swarm: active"; then
        log_error "Docker Swarm 未初始化，请先运行 swarm-init.sh"
        exit 1
    fi
    log_info "Docker Swarm 状态正常"
}

# 检查并创建网络
check_network() {
    log_info "检查网络配置..."

    if ! docker network ls | grep -q "$NETWORK_NAME"; then
        log_warn "网络 $NETWORK_NAME 不存在，正在创建..."

        # 创建overlay网络
        if docker network create \
            --driver overlay \
            --subnet "10.0.1.0/24" \
            --gateway "10.0.1.1" \
            --attachable \
            "$NETWORK_NAME"; then
            log_info "网络 $NETWORK_NAME 创建成功"
        else
            log_error "网络 $NETWORK_NAME 创建失败"
            log_error "请检查Docker Swarm状态或手动创建网络"
            exit 1
        fi
    else
        log_info "网络 $NETWORK_NAME 存在"
    fi

    # 检查网络详细信息
    local network_info=$(docker network inspect "${NETWORK_NAME}" 2>/dev/null)
    if [[ -n "$network_info" ]]; then
        local network_driver=$(echo "$network_info" | grep -o '"Driver": "[^"]*"' | cut -d'"' -f4)
        local network_scope=$(echo "$network_info" | grep -o '"Scope": "[^"]*"' | cut -d'"' -f4)
        log_info "网络驱动: $network_driver, 作用域: $network_scope"

        if [[ ! "$network_driver" =~ "overlay" ]]; then
            log_warn "网络 ${NETWORK_NAME} 不是overlay类型，可能导致Swarm服务问题"
        else
            log_info "网络配置正确：overlay网络，适用于Swarm"
        fi
    fi
}

# 清理孤立的容器
cleanup_orphaned_resources() {
    log_info "清理孤立的资源..."

    # 清理孤立的容器
    local orphaned_containers=$(docker ps -a --filter "name=${STACK_NAME}_" --format "{{.ID}}" 2>/dev/null || true)
    if [[ -n "$orphaned_containers" ]]; then
        log_warn "发现孤立的容器，正在清理..."
        echo "$orphaned_containers" | xargs -r docker rm -f 2>/dev/null || true
    fi

    # 注意：不清理网络，避免删除刚创建的ccsp-network
    log_info "孤立容器清理完成"

    # 等待清理完成
    sleep 2
}

# 生成Traefik配置文件
generate_traefik_config() {
    log_info "生成Traefik配置文件..." >&2

    local config_file="/tmp/traefik-${STACK_NAME}-$(date +%s).yml"

    cat > "$config_file" << EOF
# Traefik 内网环境配置
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API和Dashboard配置
api:
  dashboard: true
  insecure: true
  debug: false

# 健康检查端点
ping:
  entryPoint: "traefik"

# 入口点配置
entryPoints:
  web:
    address: ":80"
  traefik:
    address: ":8080"

# 服务发现配置
providers:
  swarm:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: ${NETWORK_NAME}
    watch: true

# 日志配置
log:
  level: DEBUG
  format: json

accessLog:
  format: json

# 指标配置
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# 中间件配置
http:
  middlewares:
    # 默认头部中间件
    default-headers:
      headers:
        frameDeny: true
        browserXssFilter: true
        contentTypeNosniff: true

    # 安全头部中间件
    secure-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
        accessControlMaxAge: 100
        hostsProxyHeaders:
          - "X-Forwarded-Host"
        referrerPolicy: "same-origin"
EOF

    echo "$config_file"
}

# 创建Docker Config
create_traefik_config() {
    log_info "创建Traefik配置..."

    local config_file=$(generate_traefik_config)

    # 检查配置文件是否生成成功
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件生成失败: $config_file"
        return 1
    fi

  # 使用固定的配置名称，例如与 stack 关联
    local config_name="${STACK_NAME}-traefik-config"
    export TRAEFIK_CONFIG_NAME=$config_name # 导出为环境变量，方便 compose 文件引用
    # 先尝试删除旧的同名配置，忽略错误
    docker config rm "$config_name" >/dev/null 2>&1 || true
    sleep 2 # 等待 swarm manager 同步删除操作
    # 创建新配置
    if docker config create "$config_name" "$config_file"; then
        log_info "Traefik配置创建完成: $config_name"
    else
        log_error "Traefik配置创建失败"
        rm -f "$config_file"
        return 1
    fi

    # 等待配置清理完成
    sleep 2

    # 清理临时文件
    rm -f "$config_file"
}

# 部署Traefik
deploy_traefik() {
    log_info "部署Traefik（内网环境，无SSL配置）..."

    # 检查8080端口占用情况
    if netstat -ln 2>/dev/null | grep -q ":8080 " || ss -ln 2>/dev/null | grep -q ":8080 "; then
        log_warn "端口8080已被占用，正在检查占用进程..."
        if command -v lsof &> /dev/null; then
            lsof -i :8080 2>/dev/null || true
        elif command -v netstat &> /dev/null; then
            netstat -tlnp 2>/dev/null | grep ":8080 " || true
        fi
    fi

    # 检查并清理可能存在的旧服务
    if docker service ls --format "{{.Name}}" | grep -q "^${STACK_NAME}_"; then
        log_warn "发现已存在的${STACK_NAME}服务，正在清理..."
        docker stack rm "$STACK_NAME"
        log_info "等待服务清理完成..."
        sleep 15

        # 等待端口释放
        local wait_count=0
        while netstat -ln 2>/dev/null | grep -q ":8080 " && [[ $wait_count -lt 60 ]]; do
            echo -n "."
            sleep 1
            ((wait_count++))
        done
        echo ""

        if netstat -ln 2>/dev/null | grep -q ":8080 "; then
            log_warn "端口8080仍被占用，可能需要手动清理"
        else
            log_info "端口8080已释放"
        fi
    fi

    # 创建配置
    create_traefik_config

    # 创建临时compose文件
    local compose_file="/tmp/docker-compose-traefik-${STACK_NAME}.yml"

    cat > "$compose_file" << EOF
version: '3.8'

services:
  traefik:
    image: traefik:v3.5
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "8080:80"
      - "8081:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "traefik-logs:/var/log/traefik"
    configs:
      - source: ${TRAEFIK_CONFIG_NAME:-traefik-config}
        target: /etc/traefik/traefik.yml
    networks:
      - ${NETWORK_NAME}
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      labels:
        # 启用Traefik服务发现
        - "traefik.enable=true"
        # Traefik Dashboard路由（内网环境，通过8080端口访问）
        - "traefik.http.routers.traefik-dashboard.rule=Host(\`traefik.${DOMAIN}\`)"
        - "traefik.http.routers.traefik-dashboard.entrypoints=traefik"
        - "traefik.http.routers.traefik-dashboard.service=api@internal"
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

configs:
  ${TRAEFIK_CONFIG_NAME:-traefik-config}:
    external: true

volumes:
  traefik-logs:
    driver: local

networks:
  ${NETWORK_NAME}:
    external: true
EOF



    # 部署Stack
    if docker stack deploy --compose-file "$compose_file" "$STACK_NAME"; then
        log_success "Traefik部署完成"
    else
        log_error "Stack部署失败"
        rm -f "$compose_file"
        return 1
    fi

    # 清理临时文件
    rm -f "$compose_file"
}

# 验证部署状态
verify_deployment() {
    log_info "验证Traefik部署状态..."

    # 等待服务启动
    sleep 10

    # 检查服务状态
    local service_name="${STACK_NAME}_traefik"
    local service_status=$(docker service ls --filter name="$service_name" --format "{{.Replicas}}" 2>/dev/null)

    if [[ "$service_status" == "1/1" ]]; then
        log_success "Traefik服务运行正常"
    else
        log_warn "Traefik服务状态: $service_status"
        log_info "查看服务详情："
        docker service ps "$service_name" --no-trunc 2>/dev/null || true
    fi

    # 测试Dashboard访问（通过80端口）
    local max_attempts=10
    local attempt=1

    log_info "等待Traefik Dashboard就绪..."
    while [[ $attempt -le $max_attempts ]]; do
        # 通过80端口访问Dashboard
        local dashboard_accessible=false

        # 方式1: 通过Host头访问Dashboard（8081端口）
        if curl -s --fail --connect-timeout 3 -H "Host: traefik.${DOMAIN}" http://127.0.0.1:8081/dashboard/ > /dev/null 2>&1; then
            dashboard_accessible=true
        # 方式2: 通过管理节点IP访问
        elif curl -s --fail --connect-timeout 3 -H "Host: traefik.${DOMAIN}" http://$(hostname -I | awk '{print $1}'):8081/dashboard/ > /dev/null 2>&1; then
            dashboard_accessible=true
        fi

        if $dashboard_accessible; then
            log_success "Traefik Dashboard访问正常"
            break
        else
            if [[ $attempt -eq $max_attempts ]]; then
                log_info "Dashboard正在启动中，请稍后访问"
                break
            else
                echo -n "."
                sleep 3
                ((attempt++))
            fi
        fi
    done
    echo ""
}

# 配置本地DNS解析
setup_local_dns() {
    log_info "配置本地DNS解析..."

    local hosts_entry="127.0.0.1 traefik.${DOMAIN}"

    # 检查是否已存在配置
    if grep -q "traefik.${DOMAIN}" /etc/hosts 2>/dev/null; then
        log_info "DNS解析已配置"
    else
        log_info "添加DNS解析到 /etc/hosts"
        echo "请手动添加以下行到 /etc/hosts 文件："
        echo "  $hosts_entry"
        echo ""
        echo "或运行以下命令（需要sudo权限）："
        echo "  echo '$hosts_entry' | sudo tee -a /etc/hosts"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "Traefik部署完成！"
    echo ""
    echo "=== 部署信息 ==="
    echo "Stack名称: $STACK_NAME"
    echo "网络名称: $NETWORK_NAME"
    echo "域名: $DOMAIN"
    echo ""
    echo "=== 访问地址 ==="
    echo "Traefik仪表板: http://traefik.${DOMAIN}:8081/dashboard/"
    echo "注意: 使用8081端口访问Dashboard"
    echo ""
    echo "=== 常用命令 ==="
    echo "查看Stack状态: docker stack ls"
    echo "查看服务状态: docker service ls | grep traefik"
    echo "查看服务日志: docker service logs ${STACK_NAME}_traefik -f"
    echo "删除Stack: docker stack rm $STACK_NAME"
    echo ""
    echo "=== 测试命令 ==="
    echo "curl -H \"Host: traefik.${DOMAIN}\" http://localhost:8081/dashboard/"
    echo "或配置hosts文件后直接访问: http://traefik.${DOMAIN}:8081/dashboard/"
    echo ""
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"

    echo "Traefik 内网环境部署脚本"
    echo "适用于内网环境，无SSL配置"
    echo ""

    log_info "开始部署Traefik..."
    echo ""
    log_info "配置信息："
    echo "  Stack名称: $STACK_NAME"
    echo "  网络名称: $NETWORK_NAME"
    echo "  域名: $DOMAIN"
    echo ""

    # 检查环境
    check_swarm_status
    check_network

    # 清理孤立资源
    # cleanup_orphaned_resources

    # 部署Traefik
    deploy_traefik

    # 验证部署
    verify_deployment

    # 配置DNS
    setup_local_dns

    # 显示部署信息
    show_deployment_info

    log_success "Traefik部署脚本执行完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

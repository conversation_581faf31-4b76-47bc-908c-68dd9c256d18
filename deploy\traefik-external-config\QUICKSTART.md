# Traefik 外部配置部署 - 快速开始

## 一键部署

```bash
# 1. 进入部署目录
cd deploy/traefik-external-config

# 2. 执行部署脚本
./deploy-traefik-external-config.sh
```

## 访问Dashboard

部署完成后，通过以下地址访问Traefik Dashboard：

```
http://traefik.ccsp.local:8081/dashboard/
```

## 文件说明

- `deploy-traefik-external-config.sh` - 部署脚本
- `traefik.yml` - Traefik静态配置
- `docker-compose.yml` - Docker服务配置
- `README.md` - 详细使用说明

## 自定义配置

### 修改域名
```bash
./deploy-traefik-external-config.sh -d example.local
```

### 修改Stack名称
```bash
./deploy-traefik-external-config.sh -s my-traefik
```

### 修改网络名称
```bash
./deploy-traefik-external-config.sh -n my-network
```

## 常用命令

```bash
# 查看服务状态
docker stack ls
docker service ls | grep traefik

# 查看日志
docker service logs traefik_traefik -f

# 删除服务
docker stack rm traefik
```

## 前置条件

1. Docker Swarm已初始化
2. 端口8080和8081未被占用

如果需要初始化Swarm：
```bash
docker swarm init
```

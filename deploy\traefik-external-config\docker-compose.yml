version: '3.8'

services:
  traefik:
    image: traefik:v3.5
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "8080:80"
      - "8081:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "traefik-logs:/var/log/traefik"
    configs:
      - source: ${TRAEFIK_CONFIG_NAME:-traefik-config}
        target: /etc/traefik/traefik.yml
    networks:
      - ${NETWORK_NAME:-ccsp-network}
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      labels:
        # 启用Traefik服务发现
        - "traefik.enable=true"
        # Traefik Dashboard路由（内网环境，通过8080端口访问）
        - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.${DOMAIN:-ccsp.local}`)"
        - "traefik.http.routers.traefik-dashboard.entrypoints=traefik"
        - "traefik.http.routers.traefik-dashboard.service=api@internal"
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

configs:
  ${TRAEFIK_CONFIG_NAME:-traefik-config}:
    external: true

volumes:
  traefik-logs:
    driver: local

networks:
  ${NETWORK_NAME:-ccsp-network}:
    external: true

# Traefik Dashboard 访问指南

## 访问方式

Traefik Dashboard 现在通过 **8080端口** 访问，避免与服务器上的nginx冲突。

### 方法一：配置 hosts 文件（推荐）

**Windows 系统：**
1. 以管理员身份打开记事本
2. 打开文件：`C:\Windows\System32\drivers\etc\hosts`
3. 添加以下行：
   ```
   <你的服务器IP>  traefik.ccsp.local
   ```
4. 保存文件
5. 在浏览器中访问：`http://traefik.ccsp.local:8080`

**Linux/macOS 系统：**
1. 编辑 hosts 文件：
   ```bash
   sudo nano /etc/hosts
   ```
2. 添加以下行：
   ```
   <你的服务器IP>  traefik.ccsp.local
   ```
3. 保存文件
4. 在浏览器中访问：`http://traefik.ccsp.local:8080`

### 方法二：使用 curl 命令测试

```bash
# 测试 Dashboard 访问
curl -H "Host: traefik.ccsp.local" http://<服务器IP>:8080/

# 或者如果在服务器本地
curl -H "Host: traefik.ccsp.local" http://localhost:8080/
```

## 获取服务器IP地址

如果不确定服务器IP地址，可以使用以下命令：

```bash
# 查看所有网络接口
ip addr show

# 或者查看默认路由的IP
hostname -I | awk '{print $1}'
```

## 验证部署状态

```bash
# 查看 Traefik 服务状态
docker service ls | grep traefik

# 查看 Traefik 服务日志
docker service logs traefik_traefik -f

# 查看网络状态
docker network ls | grep ccsp-network
```

## 故障排查

### 1. 无法访问 Dashboard
- 检查服务是否正常运行：`docker service ls`
- 检查服务日志：`docker service logs traefik_traefik`
- 确认 hosts 文件配置正确
- 确认服务器防火墙允许80端口访问

### 2. 服务启动失败
- 检查 Docker Swarm 状态：`docker info | grep Swarm`
- 检查网络是否存在：`docker network ls | grep ccsp-network`
- 重新部署：`./scripts/deploy-traefik.sh`

### 3. 端口冲突
- 检查8080端口占用：`netstat -tlnp | grep :8080`
- 停止冲突的服务或更改端口配置

## 安全说明

- Dashboard 现在通过域名路由访问，不直接暴露管理端口
- 仅在内网环境使用，生产环境建议添加认证中间件
- 定期检查和更新 Traefik 版本

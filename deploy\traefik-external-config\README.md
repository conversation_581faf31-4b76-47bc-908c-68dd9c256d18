# Traefik 外部配置文件部署脚本

## 概述

这是一个使用外部配置文件的Traefik部署方案，将Traefik的静态配置和Docker Compose配置都提取到独立的配置文件中，提高了配置的可维护性和可读性。

## 文件结构

```
deploy/traefik-external-config/
├── deploy-traefik-external-config.sh  # 部署脚本
├── traefik.yml                        # Traefik静态配置文件
├── docker-compose.yml                 # Docker Compose配置文件
└── README.md                          # 使用说明
```

## 主要特性

- **外部配置文件**: 使用 `traefik.yml` 管理Traefik静态配置
- **外部Compose文件**: 使用 `docker-compose.yml` 管理Docker服务配置
- **环境变量支持**: 配置文件支持 `${NETWORK_NAME}`, `${DOMAIN}` 等环境变量替换
- **集中管理**: 所有相关文件放在同一个文件夹下，便于管理
- **完整功能**: 包含完整的部署、验证和清理功能

## 使用方法

### 基本用法

```bash
# 进入部署目录
cd deploy/traefik-external-config

# 使用默认配置部署
./deploy-traefik-external-config.sh

# 指定Stack名称
./deploy-traefik-external-config.sh -s my-traefik

# 指定域名
./deploy-traefik-external-config.sh -d example.local

# 完整参数示例
./deploy-traefik-external-config.sh -s traefik -n ccsp-network -d ccsp.local
```

### 命令行参数

- `-s, --stack <NAME>`: 指定Stack名称 (默认: traefik)
- `-n, --network <NAME>`: 指定网络名称 (默认: ccsp-network)
- `-d, --domain <DOMAIN>`: 指定域名 (默认: ccsp.local)
- `-h, --help`: 显示帮助信息

## 配置文件

### 配置文件位置

- **Traefik静态配置**: `traefik.yml`
- **Docker Compose配置**: `docker-compose.yml`

### 环境变量支持

配置文件中可以使用以下环境变量：

- `${NETWORK_NAME}`: 网络名称
- `${DOMAIN}`: 域名
- `${STACK_NAME}`: Stack名称

### 配置文件示例

```yaml
# Traefik 内网环境配置
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API和Dashboard配置
api:
  dashboard: true
  insecure: true
  debug: false

# 入口点配置
entryPoints:
  web:
    address: ":80"
  traefik:
    address: ":8080"

# 服务发现配置
providers:
  swarm:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: ${NETWORK_NAME}
    watch: true
```

## 部署结果

部署完成后，可以通过以下地址访问：

- **Traefik Dashboard**: `http://traefik.ccsp.local:8081/dashboard/`
- **服务入口**: 通过8080端口访问其他服务

## 与原脚本的区别

| 特性 | 原脚本 (deploy-traefik.sh) | 新脚本 (deploy-traefik-external-config.sh) |
|------|---------------------------|-------------------------------------------|
| 配置方式 | 脚本内硬编码 | 外部配置文件 |
| Compose配置 | 脚本内生成 | 外部Compose文件 |
| 配置修改 | 需要修改脚本 | 只需修改配置文件 |
| 可维护性 | 较低 | 较高 |
| 功能完整性 | 完整 | 完整（保持一致） |
| 命令行参数 | 支持 | 支持（完全相同） |

## 注意事项

1. **前置条件**: 需要先初始化Docker Swarm集群
2. **网络要求**: 需要创建ccsp-network网络（脚本会自动创建）
3. **配置文件**: 确保以下文件存在：
   - `traefik.yml`
   - `docker-compose.yml`
4. **端口使用**: 使用8080和8081端口，确保端口未被占用
5. **内网环境**: 此脚本适用于内网环境，无SSL配置

## 故障排查

### 配置文件不存在
```
[ERROR] 配置文件不存在: config/traefik/traefik-intranet.yml
```
**解决方案**: 确保配置文件存在并包含有效的Traefik配置

### Docker Swarm未初始化
```
[ERROR] Docker Swarm 未初始化，请先运行 swarm-init.sh
```
**解决方案**: 执行 `docker swarm init` 初始化Swarm集群

### 端口被占用
```
[WARN] 端口8080已被占用
```
**解决方案**: 检查并停止占用端口的服务，或使用不同的端口配置

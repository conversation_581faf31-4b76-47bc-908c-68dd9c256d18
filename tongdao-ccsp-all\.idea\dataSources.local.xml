<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-252.23892.409">
    <data-source name="192.168.1.134" uuid="1c2bbf3f-00b5-486f-8e7b-55d559aab232">
      <database-info product="MySQL" version="8.0.35" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.35" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>sm3</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
      <ssh-properties>
        <enabled>true</enabled>
        <ssh-config-id>47aaa8b9-5996-487c-b9a0-e0bdf587eaec</ssh-config-id>
      </ssh-properties>
    </data-source>
    <data-source name="********************************************************************************************************************************** [DEBUG]" uuid="bcc5e561-077b-4e6d-b1b0-e66de4a26f49">
      <database-info product="" version="" jdbc-version="" driver-name="" driver-version="" dbms="MYSQL" />
      <schema-mapping />
    </data-source>
    <data-source name="********************************************************************************************************************************** [DEBUG]" uuid="85095036-cdbd-44a8-8ff6-3a3bbdf9f483">
      <database-info product="" version="" jdbc-version="" driver-name="" driver-version="" dbms="MYSQL" />
      <schema-mapping />
    </data-source>
  </component>
</project>